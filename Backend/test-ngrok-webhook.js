#!/usr/bin/env node

/**
 * Test Ngrok Webhook Endpoint
 * This script tests the webhook endpoint through ngrok
 */

const axios = require('axios')
const crypto = require('crypto')

// Configuration
const NGROK_URL = 'https://undenied-chronic-jayce.ngrok-free.dev'
const STRIPE_WEBHOOK_SECRET = 'whsec_5mliZlq9q7BEEwc6Q9OmkxsuJK6g3fDq'

class NgrokWebhookTester {
  constructor() {
    this.baseUrl = NGROK_URL
    this.webhookSecret = STRIPE_WEBHOOK_SECRET
  }

  // Generate proper Stripe webhook signature
  generateStripeSignature(payload, secret) {
    const timestamp = Math.floor(Date.now() / 1000)
    const signedPayload = `${timestamp}.${payload}`
    
    const signature = crypto
      .createHmac('sha256', secret)
      .update(signedPayload, 'utf8')
      .digest('hex')
    
    return `t=${timestamp},v1=${signature}`
  }

  // Test health endpoint
  async testHealth() {
    console.log('🏥 Testing health endpoint...')
    
    try {
      const response = await axios.get(`${this.baseUrl}/health`)
      
      if (response.data.status === 'OK') {
        console.log('✅ Health endpoint working')
        console.log(`   Environment: ${response.data.environment}`)
        console.log(`   Timestamp: ${response.data.timestamp}`)
        return true
      } else {
        console.log('❌ Health endpoint failed:', response.data)
        return false
      }
    } catch (error) {
      console.log('❌ Health endpoint error:', error.response?.data || error.message)
      return false
    }
  }

  // Test webhook endpoint through ngrok
  async testWebhookThroughNgrok() {
    console.log('🌐 Testing webhook through ngrok...')
    
    const testEvent = {
      id: 'evt_test_ngrok_webhook',
      object: 'event',
      api_version: '2025-08-27.basil',
      created: Math.floor(Date.now() / 1000),
      data: {
        object: {
          id: 'in_test_ngrok_invoice',
          object: 'invoice',
          status: 'paid',
          amount_paid: 999, // $9.99
          currency: 'usd'
        }
      },
      livemode: false,
      pending_webhooks: 1,
      request: {
        id: 'req_test_ngrok_request',
        idempotency_key: null
      },
      type: 'invoice.payment_succeeded'
    }

    const payload = JSON.stringify(testEvent)
    const signature = this.generateStripeSignature(payload, this.webhookSecret)

    try {
      const response = await axios.post(`${this.baseUrl}/api/webhook/stripe`, payload, {
        headers: {
          'Content-Type': 'application/json',
          'Stripe-Signature': signature
        }
      })

      if (response.data.success) {
        console.log('✅ Ngrok webhook test passed')
        console.log(`   Event ID: ${response.data.eventId}`)
        console.log(`   Event Type: ${response.data.eventType}`)
        return true
      } else {
        console.log('❌ Ngrok webhook test failed:', response.data)
        return false
      }
    } catch (error) {
      console.log('❌ Ngrok webhook test error:', error.response?.data || error.message)
      return false
    }
  }

  // Test subscription plans endpoint
  async testSubscriptionPlans() {
    console.log('💳 Testing subscription plans endpoint...')
    
    try {
      const response = await axios.get(`${this.baseUrl}/api/subscriptions/plans`)
      
      if (response.data.success && response.data.data.length > 0) {
        console.log('✅ Subscription plans endpoint working')
        console.log(`   Found ${response.data.data.length} plans:`)
        response.data.data.forEach(plan => {
          console.log(`     - ${plan.display_name}: $${plan.price_amount / 100}/${plan.billing_interval} (${plan.credits_included} credits)`)
        })
        return true
      } else {
        console.log('❌ Subscription plans test failed:', response.data)
        return false
      }
    } catch (error) {
      console.log('❌ Subscription plans test error:', error.response?.data || error.message)
      return false
    }
  }

  // Test CORS
  async testCORS() {
    console.log('🌍 Testing CORS...')
    
    try {
      const response = await axios.options(`${this.baseUrl}/api/subscriptions/plans`, {
        headers: {
          'Origin': 'https://example.com',
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type'
        }
      })

      if (response.status === 200) {
        console.log('✅ CORS preflight working')
        return true
      } else {
        console.log('❌ CORS test failed:', response.status)
        return false
      }
    } catch (error) {
      console.log('❌ CORS test error:', error.response?.data || error.message)
      return false
    }
  }

  // Run all tests
  async runAllTests() {
    console.log('🚀 Starting Ngrok Webhook Tests')
    console.log('===============================')
    console.log(`🌐 Testing URL: ${this.baseUrl}`)
    console.log('===============================')
    
    const tests = [
      { name: 'Health Check', test: () => this.testHealth() },
      { name: 'Subscription Plans', test: () => this.testSubscriptionPlans() },
      { name: 'CORS', test: () => this.testCORS() },
      { name: 'Webhook Through Ngrok', test: () => this.testWebhookThroughNgrok() }
    ]

    let passedTests = 0
    let totalTests = tests.length

    for (const { name, test } of tests) {
      console.log(`\n📋 Running test: ${name}`)
      console.log('─'.repeat(40))
      
      try {
        const result = await test()
        if (result) {
          passedTests++
          console.log(`✅ ${name} - PASSED`)
        } else {
          console.log(`❌ ${name} - FAILED`)
        }
      } catch (error) {
        console.log(`❌ ${name} - ERROR:`, error.message)
      }
    }

    console.log('\n===============================')
    console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`)
    
    if (passedTests === totalTests) {
      console.log('🎉 All ngrok tests passed! Your API is accessible through ngrok.')
      console.log('\n📝 Next steps:')
      console.log('   1. Configure this webhook URL in your Stripe Dashboard:')
      console.log(`      ${this.baseUrl}/api/webhook/stripe`)
      console.log('   2. Test a real payment using the HTML test page')
      console.log('   3. Monitor webhook events in Stripe Dashboard')
    } else {
      console.log('⚠️  Some tests failed. Please check the errors above.')
    }
    
    console.log('===============================')

    return passedTests === totalTests
  }
}

// Run the tests
if (require.main === module) {
  const tester = new NgrokWebhookTester()
  tester.runAllTests().then(success => {
    process.exit(success ? 0 : 1)
  }).catch(error => {
    console.error('Test suite error:', error)
    process.exit(1)
  })
}

module.exports = NgrokWebhookTester
