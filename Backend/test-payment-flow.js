#!/usr/bin/env node

/**
 * Comprehensive Payment Flow Test
 * Tests the complete payment flow including webhook verification
 */

const axios = require('axios')
const crypto = require('crypto')

// Configuration
const BASE_URL = 'http://localhost:3001'
const STRIPE_WEBHOOK_SECRET = 'whsec_5mliZlq9q7BEEwc6Q9OmkxsuJK6g3fDq'

// Test user credentials (you'll need to create a test user first)
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
}

// Test subscription plan price ID (will be fetched from API)
const TEST_PRICE_ID = null

class PaymentFlowTester {
  constructor() {
    this.authToken = null
    this.userId = null
  }

  async log(message) {
    console.log(`[${new Date().toISOString()}] ${message}`)
  }

  async error(message, error = null) {
    console.error(`[${new Date().toISOString()}] ❌ ${message}`)
    if (error) {
      console.error(error.response?.data || error.message || error)
    }
  }

  async success(message) {
    console.log(`[${new Date().toISOString()}] ✅ ${message}`)
  }

  // Step 1: Authenticate user
  async authenticateUser() {
    try {
      this.log('Step 1: Authenticating user...')
      
      const response = await axios.post(`${BASE_URL}/api/auth/login`, {
        email: TEST_USER.email,
        password: TEST_USER.password
      })

      if (response.data.success) {
        this.authToken = response.data.data.accessToken
        this.userId = response.data.data.user.id
        this.success(`User authenticated: ${this.userId}`)
        return true
      } else {
        this.error('Authentication failed', response.data)
        return false
      }
    } catch (error) {
      this.error('Authentication error', error)
      return false
    }
  }

  // Step 2: Get subscription plans
  async getSubscriptionPlans() {
    try {
      this.log('Step 2: Fetching subscription plans...')
      
      const response = await axios.get(`${BASE_URL}/api/subscriptions/plans`)

      if (response.data.success && response.data.data.length > 0) {
        this.success(`Found ${response.data.data.length} subscription plans`)
        console.log('Available plans:', response.data.data.map(p => ({
          name: p.display_name,
          price: p.price_amount / 100,
          priceId: p.stripe_price_id
        })))
        return response.data.data[0].stripe_price_id // Return first plan's price ID
      } else {
        this.error('No subscription plans found', response.data)
        return null
      }
    } catch (error) {
      this.error('Error fetching subscription plans', error)
      return null
    }
  }

  // Step 3: Create subscription
  async createSubscription(priceId) {
    try {
      this.log('Step 3: Creating subscription...')
      
      const response = await axios.post(`${BASE_URL}/api/subscriptions/create-subscription`, {
        priceId: priceId
      }, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.data.success) {
        this.success('Subscription created successfully')
        console.log('Subscription details:', {
          subscriptionId: response.data.data.subscription.id,
          status: response.data.data.subscription.status,
          clientSecret: response.data.data.clientSecret ? 'Present' : 'Missing'
        })
        return response.data.data
      } else {
        this.error('Subscription creation failed', response.data)
        return null
      }
    } catch (error) {
      this.error('Error creating subscription', error)
      return null
    }
  }

  // Step 4: Test webhook endpoint
  async testWebhookEndpoint() {
    try {
      this.log('Step 4: Testing webhook endpoint...')
      
      // Create a test webhook payload
      const testEvent = {
        id: 'evt_test_webhook',
        object: 'event',
        api_version: '2024-12-18.acacia',
        created: Math.floor(Date.now() / 1000),
        data: {
          object: {
            id: 'in_test_invoice',
            object: 'invoice',
            subscription: 'sub_test_subscription',
            status: 'paid',
            amount_paid: 2000,
            currency: 'usd'
          }
        },
        livemode: false,
        pending_webhooks: 1,
        request: {
          id: 'req_test_request',
          idempotency_key: null
        },
        type: 'invoice.payment_succeeded'
      }

      const payload = JSON.stringify(testEvent)
      const timestamp = Math.floor(Date.now() / 1000)
      
      // Create Stripe signature
      const signature = this.createStripeSignature(payload, timestamp, STRIPE_WEBHOOK_SECRET)
      
      const response = await axios.post(`${BASE_URL}/api/webhook/stripe`, payload, {
        headers: {
          'Content-Type': 'application/json',
          'Stripe-Signature': signature
        }
      })

      if (response.data.success) {
        this.success('Webhook endpoint working correctly')
        console.log('Webhook response:', response.data)
        return true
      } else {
        this.error('Webhook test failed', response.data)
        return false
      }
    } catch (error) {
      this.error('Webhook test error', error)
      return false
    }
  }

  // Helper: Create Stripe signature for webhook testing
  createStripeSignature(payload, timestamp, secret) {
    const signedPayload = `${timestamp}.${payload}`
    const signature = crypto
      .createHmac('sha256', secret)
      .update(signedPayload, 'utf8')
      .digest('hex')
    
    return `t=${timestamp},v1=${signature}`
  }

  // Step 5: Check user subscription status
  async checkSubscriptionStatus() {
    try {
      this.log('Step 5: Checking subscription status...')
      
      const response = await axios.get(`${BASE_URL}/api/subscriptions/current`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        }
      })

      if (response.data.success) {
        this.success('Subscription status retrieved')
        console.log('Current subscription:', response.data.data.subscription)
        console.log('Current credits:', response.data.data.credits)
        return true
      } else {
        this.error('Failed to get subscription status', response.data)
        return false
      }
    } catch (error) {
      this.error('Error checking subscription status', error)
      return false
    }
  }

  // Run complete test suite
  async runTests() {
    console.log('🚀 Starting Payment Flow Test Suite')
    console.log('=====================================')
    
    let success = true

    // Step 1: Authenticate
    if (!await this.authenticateUser()) {
      success = false
    }

    // Step 2: Get plans
    let priceId = null
    if (success) {
      priceId = await this.getSubscriptionPlans()
      if (!priceId) {
        success = false
      }
    }

    // Step 3: Create subscription
    let subscriptionData = null
    if (success && priceId) {
      subscriptionData = await this.createSubscription(priceId)
      if (!subscriptionData) {
        success = false
      }
    }

    // Step 4: Test webhook
    if (success) {
      if (!await this.testWebhookEndpoint()) {
        success = false
      }
    }

    // Step 5: Check status
    if (success) {
      if (!await this.checkSubscriptionStatus()) {
        success = false
      }
    }

    console.log('\n=====================================')
    if (success) {
      console.log('🎉 All tests passed! Payment flow is working correctly.')
    } else {
      console.log('❌ Some tests failed. Please check the errors above.')
    }
    console.log('=====================================')

    return success
  }
}

// Run the tests
if (require.main === module) {
  const tester = new PaymentFlowTester()
  tester.runTests().then(success => {
    process.exit(success ? 0 : 1)
  }).catch(error => {
    console.error('Test suite error:', error)
    process.exit(1)
  })
}

module.exports = PaymentFlowTester
