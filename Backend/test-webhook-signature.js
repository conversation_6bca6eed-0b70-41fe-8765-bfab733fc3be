#!/usr/bin/env node

/**
 * Test Stripe Webhook Signature Verification
 * This script tests the webhook endpoint with proper signature generation
 */

const axios = require('axios')
const crypto = require('crypto')

// Configuration
const BASE_URL = 'http://localhost:3001'
const STRIPE_WEBHOOK_SECRET = 'whsec_5mliZlq9q7BEEwc6Q9OmkxsuJK6g3fDq'

class WebhookTester {
  constructor() {
    this.baseUrl = BASE_URL
    this.webhookSecret = STRIPE_WEBHOOK_SECRET
  }

  // Generate proper Stripe webhook signature
  generateStripeSignature(payload, secret) {
    const timestamp = Math.floor(Date.now() / 1000)
    const signedPayload = `${timestamp}.${payload}`
    
    const signature = crypto
      .createHmac('sha256', secret)
      .update(signedPayload, 'utf8')
      .digest('hex')
    
    return `t=${timestamp},v1=${signature}`
  }

  // Test webhook with valid signature
  async testValidWebhook() {
    console.log('🧪 Testing webhook with valid signature...')
    
    const testEvent = {
      id: 'evt_test_valid_signature',
      object: 'event',
      api_version: '2025-08-27.basil',
      created: Math.floor(Date.now() / 1000),
      data: {
        object: {
          id: 'in_test_invoice_valid',
          object: 'invoice',
          subscription: 'sub_test_subscription_valid',
          status: 'paid',
          amount_paid: 999, // $9.99
          currency: 'usd',
          customer: 'cus_test_customer',
          metadata: {
            user_id: 'test-user-123'
          }
        }
      },
      livemode: false,
      pending_webhooks: 1,
      request: {
        id: 'req_test_request_valid',
        idempotency_key: null
      },
      type: 'invoice.payment_succeeded'
    }

    const payload = JSON.stringify(testEvent)
    const signature = this.generateStripeSignature(payload, this.webhookSecret)

    try {
      const response = await axios.post(`${this.baseUrl}/api/webhook/stripe`, payload, {
        headers: {
          'Content-Type': 'application/json',
          'Stripe-Signature': signature
        }
      })

      if (response.data.success) {
        console.log('✅ Valid webhook signature test passed')
        console.log(`   Event ID: ${response.data.eventId}`)
        console.log(`   Event Type: ${response.data.eventType}`)
        return true
      } else {
        console.log('❌ Valid webhook test failed:', response.data)
        return false
      }
    } catch (error) {
      console.log('❌ Valid webhook test error:', error.response?.data || error.message)
      return false
    }
  }

  // Test webhook with invalid signature
  async testInvalidWebhook() {
    console.log('🧪 Testing webhook with invalid signature...')
    
    const testEvent = {
      id: 'evt_test_invalid_signature',
      type: 'invoice.payment_succeeded',
      data: { object: { id: 'in_test_invalid' } }
    }

    const payload = JSON.stringify(testEvent)
    const invalidSignature = 't=1234567890,v1=invalid_signature_hash'

    try {
      const response = await axios.post(`${this.baseUrl}/api/webhook/stripe`, payload, {
        headers: {
          'Content-Type': 'application/json',
          'Stripe-Signature': invalidSignature
        }
      })

      // In development mode, it should still process the webhook
      if (response.data.success) {
        console.log('✅ Invalid signature handled correctly (development mode fallback)')
        return true
      } else {
        console.log('❌ Invalid signature test failed:', response.data)
        return false
      }
    } catch (error) {
      console.log('❌ Invalid signature test error:', error.response?.data || error.message)
      return false
    }
  }

  // Test webhook without signature header
  async testMissingSignature() {
    console.log('🧪 Testing webhook without signature header...')
    
    const testEvent = {
      id: 'evt_test_no_signature',
      type: 'invoice.payment_succeeded',
      data: { object: { id: 'in_test_no_sig' } }
    }

    const payload = JSON.stringify(testEvent)

    try {
      const response = await axios.post(`${this.baseUrl}/api/webhook/stripe`, payload, {
        headers: {
          'Content-Type': 'application/json'
          // No Stripe-Signature header
        }
      })

      console.log('❌ Missing signature test should have failed but succeeded:', response.data)
      return false
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Missing signature correctly rejected')
        return true
      } else {
        console.log('❌ Missing signature test unexpected error:', error.response?.data || error.message)
        return false
      }
    }
  }

  // Test different webhook event types
  async testDifferentEventTypes() {
    console.log('🧪 Testing different webhook event types...')
    
    const eventTypes = [
      'invoice.payment_succeeded',
      'invoice.payment_failed',
      'customer.subscription.updated',
      'customer.subscription.deleted',
      'unknown.event.type'
    ]

    let allPassed = true

    for (const eventType of eventTypes) {
      console.log(`   Testing event type: ${eventType}`)
      
      const testEvent = {
        id: `evt_test_${eventType.replace('.', '_')}`,
        object: 'event',
        api_version: '2025-08-27.basil',
        created: Math.floor(Date.now() / 1000),
        data: {
          object: {
            id: `obj_test_${eventType.replace('.', '_')}`,
            object: eventType.includes('invoice') ? 'invoice' : 'subscription',
            status: 'active'
          }
        },
        livemode: false,
        pending_webhooks: 1,
        request: {
          id: `req_test_${eventType.replace('.', '_')}`,
          idempotency_key: null
        },
        type: eventType
      }

      const payload = JSON.stringify(testEvent)
      const signature = this.generateStripeSignature(payload, this.webhookSecret)

      try {
        const response = await axios.post(`${this.baseUrl}/api/webhook/stripe`, payload, {
          headers: {
            'Content-Type': 'application/json',
            'Stripe-Signature': signature
          }
        })

        if (response.data.success) {
          console.log(`     ✅ ${eventType} processed successfully`)
        } else {
          console.log(`     ❌ ${eventType} failed:`, response.data)
          allPassed = false
        }
      } catch (error) {
        console.log(`     ❌ ${eventType} error:`, error.response?.data || error.message)
        allPassed = false
      }
    }

    return allPassed
  }

  // Run all tests
  async runAllTests() {
    console.log('🚀 Starting Webhook Signature Tests')
    console.log('===================================')
    
    const tests = [
      { name: 'Valid Signature', test: () => this.testValidWebhook() },
      { name: 'Invalid Signature', test: () => this.testInvalidWebhook() },
      { name: 'Missing Signature', test: () => this.testMissingSignature() },
      { name: 'Different Event Types', test: () => this.testDifferentEventTypes() }
    ]

    let passedTests = 0
    let totalTests = tests.length

    for (const { name, test } of tests) {
      console.log(`\n📋 Running test: ${name}`)
      console.log('─'.repeat(40))
      
      try {
        const result = await test()
        if (result) {
          passedTests++
          console.log(`✅ ${name} - PASSED`)
        } else {
          console.log(`❌ ${name} - FAILED`)
        }
      } catch (error) {
        console.log(`❌ ${name} - ERROR:`, error.message)
      }
    }

    console.log('\n===================================')
    console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`)
    
    if (passedTests === totalTests) {
      console.log('🎉 All webhook tests passed! Your webhook endpoint is working correctly.')
    } else {
      console.log('⚠️  Some tests failed. Please check the errors above.')
    }
    
    console.log('===================================')

    return passedTests === totalTests
  }
}

// Run the tests
if (require.main === module) {
  const tester = new WebhookTester()
  tester.runAllTests().then(success => {
    process.exit(success ? 0 : 1)
  }).catch(error => {
    console.error('Test suite error:', error)
    process.exit(1)
  })
}

module.exports = WebhookTester
